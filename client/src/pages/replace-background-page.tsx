/**
 * Página para reemplazar fondos y reajustar iluminación usando IA avanzada
 * Esta implementación sigue exactamente la documentación oficial
 */
import React, { useState, useCallback, useEffect, useRef } from "react";
import { useLocation } from "wouter";
import {
  ReplaceBackgroundOptions,
  replaceBackgroundAsync,
  checkGenerationStatus,
  replaceBackgroundWithPolling,
} from "@/services/stability-replace-background-service";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ImagePlus,
  Upload,
  ArrowLeft,
  Download,
  Sparkles,
  Paintbrush,
  Sun,
  Loader2,
  Check,
  RefreshCcw,
  ArrowRight,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useBackgroundTasks } from "@/context/BackgroundTasksContext";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export default function ReplaceBackgroundPage() {
  const [_, setLocation] = useLocation();
  const { toast } = useToast();

  // Estado para las imágenes
  const [subjectImage, setSubjectImage] = useState<File | null>(null);
  const [subjectImagePreview, setSubjectImagePreview] = useState<string | null>(
    null,
  );
  const [backgroundImage, setBackgroundImage] = useState<File | null>(null);
  const [backgroundImagePreview, setBackgroundImagePreview] = useState<
    string | null
  >(null);
  const [lightImage, setLightImage] = useState<File | null>(null);
  const [lightImagePreview, setLightImagePreview] = useState<string | null>(
    null,
  );
  const [resultImage, setResultImage] = useState<string | null>(null);

  // Estado para los inputs de texto
  const [backgroundPrompt, setBackgroundPrompt] =
    useState<string>("cinematic lighting");
  const [foregroundPrompt, setForegroundPrompt] = useState<string>("");
  const [negativePrompt, setNegativePrompt] = useState<string>("");

  // Estado para los controles de ajuste
  const [preserveOriginalSubject, setPreserveOriginalSubject] =
    useState<number>(0.6);
  const [originalBackgroundDepth, setOriginalBackgroundDepth] =
    useState<number>(0.5);
  const [keepOriginalBackground, setKeepOriginalBackground] =
    useState<boolean>(false);
  const [lightSourceDirection, setLightSourceDirection] =
    useState<string>("none");
  const [lightSourceStrength, setLightSourceStrength] = useState<number>(0.3);
  const [seed, setSeed] = useState<number>(0);
  const [outputFormat, setOutputFormat] = useState<"webp" | "jpeg" | "png">(
    "webp",
  );

  // Estado para el proceso
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [progress, setProgress] = useState<number>(0);
  const [generationId, setGenerationId] = useState<string | null>(null);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const [originalName, setOriginalName] = useState<string>("");

  // Limpiar intervalos al desmontar componente
  useEffect(() => {
    return () => {
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
    };
  }, []);

  // Manejar la selección de imagen del sujeto
  const handleSubjectImageSelect = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // Validar que sea una imagen
      if (!file.type.startsWith("image/")) {
        toast({
          title: "Formato no soportado",
          description: "Por favor, selecciona un archivo de imagen válido.",
          variant: "destructive",
        });
        return;
      }

      // Guardar el nombre original para usarlo después
      setOriginalName(file.name);

      // Crear URL para previsualización
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setSubjectImagePreview(result);
        setSubjectImage(file);
        setResultImage(null); // Limpiar resultado anterior
      };
      reader.readAsDataURL(file);

      // Mostrar mensaje de éxito
      toast({
        title: "Imagen cargada",
        description:
          "Ahora puedes reemplazar el fondo y ajustar la iluminación.",
      });
    },
    [toast],
  );

  // Manejar la selección de imagen de fondo
  const handleBackgroundImageSelect = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // Validar que sea una imagen
      if (!file.type.startsWith("image/")) {
        toast({
          title: "Formato no soportado",
          description: "Por favor, selecciona un archivo de imagen válido.",
          variant: "destructive",
        });
        return;
      }

      // Crear URL para previsualización
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setBackgroundImagePreview(result);
        setBackgroundImage(file);
      };
      reader.readAsDataURL(file);

      // Mostrar mensaje de éxito
      toast({
        title: "Imagen de fondo cargada",
        description: "Esta imagen se usará como referencia para el fondo.",
      });
    },
    [toast],
  );

  // Manejar la selección de imagen de iluminación
  const handleLightImageSelect = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // Validar que sea una imagen
      if (!file.type.startsWith("image/")) {
        toast({
          title: "Formato no soportado",
          description: "Por favor, selecciona un archivo de imagen válido.",
          variant: "destructive",
        });
        return;
      }

      // Crear URL para previsualización
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setLightImagePreview(result);
        setLightImage(file);
      };
      reader.readAsDataURL(file);

      // Mostrar mensaje de éxito
      toast({
        title: "Imagen de iluminación cargada",
        description:
          "Esta imagen se usará como referencia para la iluminación.",
      });
    },
    [toast],
  );

  // Usar el sistema de tareas en segundo plano
  const { addTask, updateTask, getTask } = useBackgroundTasks();

  // Iniciar el proceso de reemplazo de fondo
  const handleProcessImage = useCallback(async () => {
    if (!subjectImage) {
      toast({
        title: "Faltan datos",
        description: "Por favor, selecciona al menos una imagen del sujeto.",
        variant: "destructive",
      });
      return;
    }

    // Verificar que se proporciona al menos una forma de generar el fondo
    if (!backgroundPrompt && !backgroundImage) {
      toast({
        title: "Faltan datos",
        description:
          "Proporciona un prompt de fondo o una imagen de referencia.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    setProgress(0);

    // Preparar opciones
    const options: ReplaceBackgroundOptions = {
      subjectImage,
      backgroundPrompt: backgroundPrompt || undefined,
      backgroundReference: backgroundImage || undefined,
      foregroundPrompt: foregroundPrompt || undefined,
      negativePrompt: negativePrompt || undefined,
      preserveOriginalSubject,
      originalBackgroundDepth,
      keepOriginalBackground,
      lightSourceDirection: (lightSourceDirection !== "none"
        ? lightSourceDirection
        : undefined) as any,
      lightReference: lightImage || undefined,
      lightSourceStrength:
        lightSourceDirection !== "none" || lightImage
          ? lightSourceStrength
          : undefined,
      seed: seed || undefined,
      outputFormat,
    };

    // Crear una tarea en segundo plano
    const taskId = addTask({
      type: "replace-background",
      status: "processing",
      progress: 0,
      message: "Preparando imagen...",
      metadata: {
        originalFileName: originalName || subjectImage.name,
        taskTitle: "Reemplazar fondo",
        taskDescription:
          "Procesando imagen para reemplazar el fondo y ajustar iluminación",
      },
    });

    // Función para actualizar el progreso tanto en la UI local como en la tarea en segundo plano
    const updateProgressCallback = (
      progressValue: number,
      message?: string,
    ) => {
      setProgress(progressValue);
      updateTask(taskId, {
        progress: progressValue,
        message: message || `Progreso: ${progressValue}%`,
      });
    };

    try {
      toast({
        title: "Iniciando proceso",
        description:
          "Esto puede tardar varios minutos. Puedes navegar a otras secciones mientras se procesa.",
      });

      // Usar la función de polling mejorada que maneja todo el proceso automáticamente
      const resultUrl = await replaceBackgroundWithPolling(
        options,
        updateProgressCallback,
        40, // Aumentar a 40 intentos (aproximadamente 6-7 minutos)
        10000, // 10 segundos entre consultas
      );

      // Si llegamos aquí, el proceso se completó exitosamente
      setResultImage(resultUrl);
      setProgress(100);
      setIsProcessing(false);

      // Actualizar la tarea como completada
      updateTask(taskId, {
        status: "completed",
        progress: 100,
        message: "Proceso completado con éxito",
        result: {
          url: resultUrl,
        },
      });

      toast({
        title: "¡Listo!",
        description: "El fondo ha sido reemplazado exitosamente.",
      });
    } catch (error) {
      console.error("Error al procesar la imagen:", error);

      // Restablecer estado
      setIsProcessing(false);
      setProgress(0);

      // Determinar tipo de error para mensajes más claros
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Ocurrió un error al procesar la imagen";

      // Identificar si es un error de tiempo de espera
      const isTimeoutError =
        errorMessage.includes("tardando demasiado") ||
        errorMessage.includes("tiempo de espera") ||
        errorMessage.includes("agotado");

      // Actualizar la tarea con el error
      updateTask(taskId, {
        status: "error",
        progress: 0,
        message: isTimeoutError
          ? "Tiempo de espera excedido"
          : "Error en el procesamiento",
        result: {
          error: isTimeoutError
            ? "El proceso está tardando demasiado. Intenta con una imagen más simple, ajusta los parámetros o prueba más tarde."
            : errorMessage,
        },
      });

      // Mensajes más claros según el tipo de error
      toast({
        title: isTimeoutError ? "Tiempo de espera excedido" : "Error",
        description: isTimeoutError
          ? "El proceso está tardando demasiado. Intenta con una imagen más simple, ajusta los parámetros o prueba más tarde."
          : errorMessage,
        variant: "destructive",
      });
    }
  }, [
    subjectImage,
    backgroundPrompt,
    backgroundImage,
    foregroundPrompt,
    negativePrompt,
    preserveOriginalSubject,
    originalBackgroundDepth,
    keepOriginalBackground,
    lightSourceDirection,
    lightImage,
    lightSourceStrength,
    seed,
    outputFormat,
    toast,
    addTask,
    updateTask,
  ]);

  // Cancelar el proceso
  const handleCancel = useCallback(() => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
    }

    setIsProcessing(false);
    setProgress(0);

    toast({
      title: "Proceso cancelado",
      description: "Has cancelado el proceso de reemplazo de fondo.",
    });
  }, [toast]);

  // Descargar imagen resultante
  const handleDownload = useCallback(() => {
    if (!resultImage) return;

    const link = document.createElement("a");
    link.href = resultImage;
    link.download = `replaced_bg_${originalName || "image"}.${outputFormat}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [resultImage, originalName, outputFormat]);

  // Navegar de vuelta
  const navigate = (path: string) => {
    setLocation(path);
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-5xl">
      <div className="flex items-center mb-6">
        <Button
          variant="outline"
          size="icon"
          onClick={() => navigate("/visual-tools-hub")}
          className="mr-2"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold">Reemplazar Fondo e Iluminación</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Panel de imagen original */}
        <Card className="h-full">
          <CardHeader>
            <CardTitle>Imagen Original</CardTitle>
            <CardDescription>
              Sube la imagen cuyo fondo quieres reemplazar
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center">
            {subjectImagePreview ? (
              <div className="relative w-full h-64 md:h-80 bg-muted rounded-md overflow-hidden">
                <img
                  src={subjectImagePreview}
                  alt="Original"
                  className="w-full h-full object-contain"
                />
                <Button
                  variant="secondary"
                  size="sm"
                  className="absolute bottom-2 right-2"
                  onClick={() =>
                    document.getElementById("subjectImageInput")?.click()
                  }
                >
                  <RefreshCcw className="mr-2 h-4 w-4" />
                  Cambiar
                </Button>
              </div>
            ) : (
              <div
                className="w-full h-64 md:h-80 bg-muted rounded-md flex flex-col items-center justify-center cursor-pointer"
                onClick={() =>
                  document.getElementById("subjectImageInput")?.click()
                }
              >
                <ImagePlus size={48} className="text-muted-foreground mb-2" />
                <p className="text-sm text-muted-foreground">
                  Haz clic para subir una imagen
                </p>
              </div>
            )}
            <input
              id="subjectImageInput"
              type="file"
              accept="image/jpeg,image/png,image/webp"
              onChange={handleSubjectImageSelect}
              className="hidden"
            />
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button
              variant="outline"
              onClick={() =>
                document.getElementById("subjectImageInput")?.click()
              }
              disabled={isProcessing}
            >
              <Upload className="mr-2 h-4 w-4" />
              Subir Imagen
            </Button>
          </CardFooter>
        </Card>

        {/* Panel de resultado */}
        <Card className="h-full">
          <CardHeader>
            <CardTitle>Resultado</CardTitle>
            <CardDescription>
              Imagen con fondo reemplazado e iluminación ajustada
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center">
            {isProcessing ? (
              <div className="w-full h-64 md:h-80 bg-muted rounded-md flex flex-col items-center justify-center">
                <Loader2 size={48} className="text-primary animate-spin mb-2" />
                <p className="text-sm text-muted-foreground mb-4">
                  Procesando imagen...
                </p>
                <Progress value={progress} className="w-3/4 mb-2" />
                <p className="text-xs text-muted-foreground">
                  {progress < 100
                    ? `${Math.round(progress)}% completado`
                    : "Finalizando..."}
                </p>
              </div>
            ) : resultImage ? (
              <div className="relative w-full h-64 md:h-80 bg-muted rounded-md overflow-hidden">
                <img
                  src={resultImage}
                  alt="Resultado"
                  className="w-full h-full object-contain"
                />
                <Button
                  variant="secondary"
                  size="sm"
                  className="absolute bottom-2 right-2"
                  onClick={handleDownload}
                >
                  <Download className="mr-2 h-4 w-4" />
                  Descargar
                </Button>
              </div>
            ) : (
              <div className="w-full h-64 md:h-80 bg-muted rounded-md flex flex-col items-center justify-center">
                <Sparkles size={48} className="text-muted-foreground mb-2" />
                <p className="text-sm text-muted-foreground">
                  El resultado aparecerá aquí
                </p>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-center">
            {isProcessing ? (
              <Button variant="destructive" onClick={handleCancel}>
                Cancelar Proceso
              </Button>
            ) : resultImage ? (
              <Button variant="default" onClick={handleDownload}>
                <Download className="mr-2 h-4 w-4" />
                Descargar Imagen
              </Button>
            ) : (
              <Button
                variant="default"
                disabled={
                  !subjectImage || (!backgroundPrompt && !backgroundImage)
                }
                onClick={handleProcessImage}
              >
                <Sparkles className="mr-2 h-4 w-4" />
                Procesar Imagen
              </Button>
            )}
          </CardFooter>
        </Card>
      </div>

      {/* Opciones de configuración */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Opciones de Generación</CardTitle>
          <CardDescription>
            Configura cómo quieres que se reemplace el fondo
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="backgroundPrompt">Prompt de Fondo</Label>
                <Textarea
                  id="backgroundPrompt"
                  placeholder="Describe cómo quieres que sea el fondo..."
                  value={backgroundPrompt}
                  onChange={(e) => setBackgroundPrompt(e.target.value)}
                  disabled={isProcessing}
                  className="resize-none"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Puedes usar un prompt, una imagen de referencia o ambos. Escribe en español - se traduce automáticamente.
                </p>
              </div>

              <div>
                <Label>Imagen de Referencia para el Fondo (Opcional)</Label>
                <div className="mt-2">
                  <div className="flex items-center space-x-2">
                    {backgroundImagePreview && (
                      <div className="w-12 h-12 bg-muted rounded overflow-hidden">
                        <img
                          src={backgroundImagePreview}
                          alt="Fondo"
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        document.getElementById("backgroundImageInput")?.click()
                      }
                      disabled={isProcessing}
                    >
                      <Upload className="mr-2 h-4 w-4" />
                      {backgroundImage ? "Cambiar" : "Subir"} Imagen
                    </Button>
                    {backgroundImage && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setBackgroundImage(null);
                          setBackgroundImagePreview(null);
                        }}
                        disabled={isProcessing}
                      >
                        Eliminar
                      </Button>
                    )}
                  </div>
                  <input
                    id="backgroundImageInput"
                    type="file"
                    accept="image/jpeg,image/png,image/webp"
                    onChange={handleBackgroundImageSelect}
                    className="hidden"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="foregroundPrompt">
                  Prompt del Sujeto (Opcional)
                </Label>
                <Textarea
                  id="foregroundPrompt"
                  placeholder="Describe el sujeto para evitar que elementos del fondo se filtren..."
                  value={foregroundPrompt}
                  onChange={(e) => setForegroundPrompt(e.target.value)}
                  disabled={isProcessing}
                  className="resize-none"
                />
              </div>

              <div>
                <Label htmlFor="negativePrompt">
                  Prompt Negativo (Opcional)
                </Label>
                <Textarea
                  id="negativePrompt"
                  placeholder="Describe lo que no quieres ver en la imagen resultante..."
                  value={negativePrompt}
                  onChange={(e) => setNegativePrompt(e.target.value)}
                  disabled={isProcessing}
                  className="resize-none"
                />
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center">
                  <Label htmlFor="preserveOriginalSubject">
                    Preservar Sujeto Original
                  </Label>
                  <span className="text-sm text-muted-foreground">
                    {preserveOriginalSubject.toFixed(2)}
                  </span>
                </div>
                <Slider
                  id="preserveOriginalSubject"
                  min={0}
                  max={1}
                  step={0.05}
                  value={[preserveOriginalSubject]}
                  onValueChange={(values) =>
                    setPreserveOriginalSubject(values[0])
                  }
                  disabled={isProcessing}
                  className="mt-2"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  1.0 es una coincidencia exacta, 0.0 tendrá nuevas cualidades
                  de iluminación
                </p>
              </div>

              <div>
                <div className="flex justify-between items-center">
                  <Label htmlFor="originalBackgroundDepth">
                    Profundidad del Fondo
                  </Label>
                  <span className="text-sm text-muted-foreground">
                    {originalBackgroundDepth.toFixed(2)}
                  </span>
                </div>
                <Slider
                  id="originalBackgroundDepth"
                  min={0}
                  max={1}
                  step={0.05}
                  value={[originalBackgroundDepth]}
                  onValueChange={(values) =>
                    setOriginalBackgroundDepth(values[0])
                  }
                  disabled={isProcessing}
                  className="mt-2"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="keepOriginalBackground"
                  checked={keepOriginalBackground}
                  onCheckedChange={setKeepOriginalBackground}
                  disabled={isProcessing}
                />
                <Label htmlFor="keepOriginalBackground">
                  Mantener Fondo Original
                </Label>
              </div>

              <div>
                <Label htmlFor="lightSourceDirection">
                  Dirección de la Luz
                </Label>
                <Select
                  value={lightSourceDirection}
                  onValueChange={setLightSourceDirection}
                  disabled={isProcessing}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Selecciona dirección de la luz" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">
                      Sin dirección específica
                    </SelectItem>
                    <SelectItem value="above">Desde arriba</SelectItem>
                    <SelectItem value="below">Desde abajo</SelectItem>
                    <SelectItem value="left">Desde la izquierda</SelectItem>
                    <SelectItem value="right">Desde la derecha</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Imagen de Referencia para Iluminación (Opcional)</Label>
                <div className="mt-2">
                  <div className="flex items-center space-x-2">
                    {lightImagePreview && (
                      <div className="w-12 h-12 bg-muted rounded overflow-hidden">
                        <img
                          src={lightImagePreview}
                          alt="Iluminación"
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        document.getElementById("lightImageInput")?.click()
                      }
                      disabled={isProcessing}
                    >
                      <Upload className="mr-2 h-4 w-4" />
                      {lightImage ? "Cambiar" : "Subir"} Imagen
                    </Button>
                    {lightImage && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setLightImage(null);
                          setLightImagePreview(null);
                        }}
                        disabled={isProcessing}
                      >
                        Eliminar
                      </Button>
                    )}
                  </div>
                  <input
                    id="lightImageInput"
                    type="file"
                    accept="image/jpeg,image/png,image/webp"
                    onChange={handleLightImageSelect}
                    className="hidden"
                  />
                </div>
              </div>

              {(lightSourceDirection !== "none" || lightImage) && (
                <div>
                  <div className="flex justify-between items-center">
                    <Label htmlFor="lightSourceStrength">
                      Intensidad de la Luz
                    </Label>
                    <span className="text-sm text-muted-foreground">
                      {lightSourceStrength.toFixed(2)}
                    </span>
                  </div>
                  <Slider
                    id="lightSourceStrength"
                    min={0}
                    max={1}
                    step={0.05}
                    value={[lightSourceStrength]}
                    onValueChange={(values) =>
                      setLightSourceStrength(values[0])
                    }
                    disabled={isProcessing}
                    className="mt-2"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    1.0 es más brillante, 0.0 es más tenue
                  </p>
                </div>
              )}

              <div>
                <Label htmlFor="seed">Seed (0 = aleatorio)</Label>
                <Input
                  id="seed"
                  type="number"
                  min={0}
                  max={4294967294}
                  value={seed}
                  onChange={(e) => setSeed(parseInt(e.target.value) || 0)}
                  disabled={isProcessing}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="outputFormat">Formato de Salida</Label>
                <Select
                  value={outputFormat}
                  onValueChange={(value: "webp" | "jpeg" | "png") =>
                    setOutputFormat(value)
                  }
                  disabled={isProcessing}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Selecciona formato" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="webp">WebP (recomendado)</SelectItem>
                    <SelectItem value="jpeg">JPEG</SelectItem>
                    <SelectItem value="png">PNG</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button
            variant="default"
            size="lg"
            disabled={
              !subjectImage ||
              (!backgroundPrompt && !backgroundImage) ||
              isProcessing
            }
            onClick={handleProcessImage}
          >
            {isProcessing ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Paintbrush className="mr-2 h-4 w-4" />
            )}
            {isProcessing
              ? "Procesando..."
              : "Reemplazar Fondo y Reajustar Iluminación"}
          </Button>
        </CardFooter>
      </Card>

      {/* Descripción o instrucciones */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Acerca de Esta Herramienta</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p>
              Esta herramienta usa la API de Stability AI para reemplazar el
              fondo de una imagen y ajustar la iluminación en un solo paso.
              Algunas cosas que puedes hacer:
            </p>
            <ul className="list-disc pl-6 space-y-1">
              <li>
                Reemplazar fondos con imágenes generadas por IA basadas en
                prompts
              </li>
              <li>Usar una imagen de referencia para el estilo del fondo</li>
              <li>Ajustar la iluminación en imágenes sub o sobreexpuestas</li>
              <li>Modificar la dirección e intensidad de la luz</li>
              <li>Controlar cuánto del sujeto original se preserva</li>
            </ul>
            <p className="text-sm text-muted-foreground mt-4">
              Nota: Este proceso es asíncrono y puede tardar hasta 1-2 minutos
              en completarse. Una vez enviada la solicitud, deberás esperar a
              que se procese en los servidores de Stability AI.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
