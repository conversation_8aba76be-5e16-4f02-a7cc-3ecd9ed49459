/**
 * Servicio para reemplazar fondos y reajustar iluminación usando la API de Stability AI
 * Implementación exacta siguiendo la documentación oficial
 */

/**
 * Traduce prompts del español al inglés para Stability AI
 * Stability AI solo acepta prompts en inglés
 */
function translatePromptToEnglish(spanishPrompt: string): string {
  if (!spanishPrompt || spanishPrompt.trim() === '') return spanishPrompt;

  // Diccionario básico de traducciones comunes para prompts de IA
  const translations: Record<string, string> = {
    // Lugares y ambientes
    'playa': 'beach',
    'montaña': 'mountain',
    'bosque': 'forest',
    'ciudad': 'city',
    'campo': 'countryside',
    'jardín': 'garden',
    'parque': 'park',
    'casa': 'house',
    'oficina': 'office',
    'estudio': 'studio',
    'sala': 'living room',
    'cocina': 'kitchen',
    'dormitorio': 'bedroom',
    'baño': 'bathroom',

    // Colores
    'rojo': 'red',
    'azul': 'blue',
    'verde': 'green',
    'amarillo': 'yellow',
    'negro': 'black',
    'blanco': 'white',
    'gris': 'gray',
    'rosa': 'pink',
    'morado': 'purple',
    'naranja': 'orange',
    'marrón': 'brown',

    // Tiempo y clima
    'soleado': 'sunny',
    'nublado': 'cloudy',
    'lluvioso': 'rainy',
    'nevado': 'snowy',
    'atardecer': 'sunset',
    'amanecer': 'sunrise',
    'noche': 'night',
    'día': 'day',
    'mañana': 'morning',
    'tarde': 'afternoon',

    // Estilos y calidad
    'hermoso': 'beautiful',
    'elegante': 'elegant',
    'moderno': 'modern',
    'clásico': 'classic',
    'vintage': 'vintage',
    'minimalista': 'minimalist',
    'colorido': 'colorful',
    'brillante': 'bright',
    'oscuro': 'dark',
    'suave': 'soft',
    'dramático': 'dramatic',
    'profesional': 'professional',
    'artístico': 'artistic',

    // Objetos comunes
    'coche': 'car',
    'auto': 'car',
    'automóvil': 'car',
    'bicicleta': 'bicycle',
    'moto': 'motorcycle',
    'avión': 'airplane',
    'barco': 'boat',
    'tren': 'train',
    'edificio': 'building',
    'árbol': 'tree',
    'flor': 'flower',
    'flores': 'flowers',
    'animal': 'animal',
    'perro': 'dog',
    'gato': 'cat',
    'pájaro': 'bird',
    'persona': 'person',
    'gente': 'people',

    // Palabras de conexión y descriptivas
    'con': 'with',
    'sin': 'without',
    'en': 'in',
    'sobre': 'on',
    'debajo': 'under',
    'cerca': 'near',
    'lejos': 'far',
    'grande': 'big',
    'pequeño': 'small',
    'alto': 'tall',
    'bajo': 'short',
    'nuevo': 'new',
    'viejo': 'old',
    'joven': 'young',
    'limpio': 'clean',
    'sucio': 'dirty',
    'rápido': 'fast',
    'lento': 'slow',
    'caliente': 'hot',
    'frío': 'cold',
    'cálido': 'warm',
    'fresco': 'cool',
  };

  let translatedPrompt = spanishPrompt.toLowerCase();

  // Aplicar traducciones palabra por palabra
  Object.entries(translations).forEach(([spanish, english]) => {
    const regex = new RegExp(`\\b${spanish}\\b`, 'gi');
    translatedPrompt = translatedPrompt.replace(regex, english);
  });

  // Si el prompt sigue siendo muy similar al original (pocas traducciones),
  // usar traducciones de frases comunes completas
  const phraseTranslations: Record<string, string> = {
    'un hermoso atardecer en la playa': 'a beautiful sunset on the beach',
    'una playa tropical': 'a tropical beach',
    'un bosque encantado': 'an enchanted forest',
    'una ciudad moderna': 'a modern city',
    'un jardín florido': 'a blooming garden',
    'una oficina moderna': 'a modern office',
    'un estudio fotográfico': 'a photography studio',
    'fondo blanco': 'white background',
    'fondo negro': 'black background',
    'fondo transparente': 'transparent background',
    'iluminación profesional': 'professional lighting',
    'iluminación suave': 'soft lighting',
    'iluminación dramática': 'dramatic lighting',
    'luz natural': 'natural light',
    'luz artificial': 'artificial light',
  };

  Object.entries(phraseTranslations).forEach(([spanish, english]) => {
    if (spanishPrompt.toLowerCase().includes(spanish)) {
      translatedPrompt = english;
    }
  });

  console.log(`🌐 Prompt traducido: "${spanishPrompt}" → "${translatedPrompt}"`);
  return translatedPrompt;
}

/**
 * Opciones para la solicitud de reemplazo de fondo y reajuste de iluminación
 */
export interface ReplaceBackgroundOptions {
  /** Imagen que contiene el sujeto cuyo fondo quieres cambiar */
  subjectImage: File;

  /** Descripción de lo que deseas ver en el fondo (requerido si no se proporciona backgroundReference) */
  backgroundPrompt?: string;

  /** Imagen cuyo estilo deseas usar en el fondo (requerido si no se proporciona backgroundPrompt) */
  backgroundReference?: File;

  /** Descripción del sujeto para evitar que elementos del fondo se filtren */
  foregroundPrompt?: string;

  /** Texto que describe lo que no deseas ver en la imagen resultante */
  negativePrompt?: string;

  /** Cuánto preservar del sujeto original (0-1) */
  preserveOriginalSubject?: number;

  /** Controla el fondo generado para que tenga la misma profundidad que la imagen del sujeto original (0-1) */
  originalBackgroundDepth?: number;

  /** Si se debe mantener el fondo de la imagen original */
  keepOriginalBackground?: boolean;

  /** Dirección de la fuente de luz: 'above', 'below', 'left', 'right' */
  lightSourceDirection?: "above" | "below" | "left" | "right";

  /** Imagen con la iluminación deseada */
  lightReference?: File;

  /** Controla la intensidad de la fuente de luz (0-1) */
  lightSourceStrength?: number;

  /** Valor específico para guiar la 'aleatoriedad' de la generación */
  seed?: number;

  /** Formato de salida de la imagen generada */
  outputFormat?: "jpeg" | "png" | "webp";
}

/**
 * Respuesta inicial de la API de reemplazo de fondo
 */
interface InitialResponse {
  success: boolean;
  id: string;
  message?: string;
  error?: string;
}

/**
 * Respuesta final con la imagen procesada
 */
interface FinalResponse {
  success: boolean;
  image_url?: string;
  url?: string; // Para compatibilidad hacia atrás
  seed?: number;
  finish_reason?: string;
  finishReason?: string; // Para compatibilidad hacia atrás
  error?: string;
  status?: string;
  message?: string;
}

/**
 * Solicita el reemplazo del fondo y reajuste de iluminación de una imagen
 * Este método inicia el proceso de generación asíncrona
 * @param options Opciones para la solicitud
 * @returns Promise con el ID de la generación
 */
export async function replaceBackgroundAsync(
  options: ReplaceBackgroundOptions,
): Promise<InitialResponse> {
  try {
    console.log("Iniciando solicitud de reemplazo de fondo...");

    // Validar opciones requeridas
    if (!options.subjectImage) {
      throw new Error("Se requiere una imagen del sujeto");
    }

    if (!options.backgroundPrompt && !options.backgroundReference) {
      throw new Error(
        "Se requiere un prompt de fondo o una imagen de referencia",
      );
    }

    // Crear FormData y agregar parámetros
    const formData = new FormData();

    // Agregar imagen del sujeto
    formData.append("subject_image", options.subjectImage);

    // Agregar prompt de fondo si está disponible (traducido al inglés)
    if (options.backgroundPrompt) {
      console.log(`🔄 Traduciendo background prompt: "${options.backgroundPrompt}"`);
      const translatedBackgroundPrompt = translatePromptToEnglish(options.backgroundPrompt);
      console.log(`✅ Background prompt traducido: "${translatedBackgroundPrompt}"`);
      formData.append("background_prompt", translatedBackgroundPrompt);
    }

    // Agregar imagen de referencia del fondo si está disponible
    if (options.backgroundReference) {
      formData.append("background_reference", options.backgroundReference);
    }

    // Agregar parámetros opcionales si están presentes (traducidos al inglés)
    if (options.foregroundPrompt) {
      console.log(`🔄 Traduciendo foreground prompt: "${options.foregroundPrompt}"`);
      const translatedForegroundPrompt = translatePromptToEnglish(options.foregroundPrompt);
      console.log(`✅ Foreground prompt traducido: "${translatedForegroundPrompt}"`);
      formData.append("foreground_prompt", translatedForegroundPrompt);
    }

    if (options.negativePrompt) {
      console.log(`🔄 Traduciendo negative prompt: "${options.negativePrompt}"`);
      const translatedNegativePrompt = translatePromptToEnglish(options.negativePrompt);
      console.log(`✅ Negative prompt traducido: "${translatedNegativePrompt}"`);
      formData.append("negative_prompt", translatedNegativePrompt);
    }

    if (options.preserveOriginalSubject !== undefined) {
      formData.append(
        "preserve_original_subject",
        options.preserveOriginalSubject.toString(),
      );
    }

    if (options.originalBackgroundDepth !== undefined) {
      formData.append(
        "original_background_depth",
        options.originalBackgroundDepth.toString(),
      );
    }

    if (options.keepOriginalBackground !== undefined) {
      formData.append(
        "keep_original_background",
        options.keepOriginalBackground.toString(),
      );
    }

    if (options.lightSourceDirection) {
      formData.append("light_source_direction", options.lightSourceDirection);
    }

    if (options.lightReference) {
      formData.append("light_reference", options.lightReference);
    }

    // Agregar light_source_strength solo si tenemos light_reference o light_source_direction
    if (
      options.lightSourceStrength !== undefined &&
      (options.lightReference || options.lightSourceDirection)
    ) {
      formData.append(
        "light_source_strength",
        options.lightSourceStrength.toString(),
      );
    }

    if (options.seed !== undefined) {
      formData.append("seed", options.seed.toString());
    }

    // Formato de salida (por defecto webp)
    formData.append("output_format", options.outputFormat || "webp");

    // Enviar solicitud al endpoint del servidor
    const response = await fetch("/api/stability-replace-bg/replace", {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.error || `Error ${response.status}: ${response.statusText}`,
      );
    }

    const data = await response.json();

    if (!data.success || !data.id) {
      throw new Error(
        data.error || "No se pudo iniciar el proceso de generación",
      );
    }

    console.log("Solicitud enviada exitosamente, ID:", data.id);

    return {
      success: true,
      id: data.id,
      message: data.message || "Solicitud enviada exitosamente",
    };
  } catch (error) {
    console.error("Error al solicitar reemplazo de fondo:", error);
    return {
      success: false,
      id: "",
      error: error instanceof Error ? error.message : "Error desconocido",
    };
  }
}

/**
 * Consulta el estado de una generación en curso
 * @param generationId ID de la generación
 * @returns Promise con la respuesta de la consulta
 */
export async function checkGenerationStatus(
  generationId: string,
): Promise<FinalResponse> {
  try {
    if (!generationId) {
      throw new Error("Se requiere un ID de generación");
    }

    console.log("Consultando estado de generación:", generationId);

    // Utilizamos el endpoint correcto para verificar el estado
    const response = await fetch(
      `/api/stability-replace-bg/status/${generationId}`,
      {
        method: "GET",
      },
    );

    const data = await response.json();

    // Log para debugging
    console.log("🔍 Respuesta del servidor:", {
      status: response.status,
      ok: response.ok,
      data: data
    });

    // Si el proceso sigue en curso
    if (response.status === 202) {
      console.log("⏳ Proceso en progreso (202)");
      return {
        success: true,
        status: "IN_PROGRESS",
        message: "La generación está en progreso",
      };
    }

    // Si hubo un error
    if (!response.ok) {
      console.log("❌ Error en respuesta:", response.status, data);
      return {
        success: false,
        error: data.error || `Error ${response.status}: ${response.statusText}`,
      };
    }

    // Si el proceso se completó exitosamente
    if (data.success && data.image_url) {
      console.log("✅ Proceso completado exitosamente con image_url");
      return {
        success: true,
        url: data.image_url,
        seed: data.seed,
        finishReason: data.finish_reason,
      };
    }

    // Si está en progreso según el campo status
    if (data.success && data.status === "IN_PROGRESS") {
      console.log("⏳ Proceso en progreso según status field");
      return {
        success: true,
        status: "IN_PROGRESS",
      };
    }

    // Otros casos
    console.log("❓ Respuesta desconocida:", data);
    return {
      success: false,
      error: data.error || "Respuesta desconocida del servidor",
    };
  } catch (error) {
    console.error("Error al consultar estado de generación:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido",
    };
  }
}

/**
 * Obtiene un mensaje descriptivo basado en el progreso y la fase del procesamiento
 * @param progress Valor de progreso (0-100)
 * @param attempts Número de intentos actuales
 * @param maxAttempts Número máximo de intentos
 * @returns Mensaje descriptivo del progreso
 */
function getProgressMessage(
  progress: number,
  attempts: number,
  maxAttempts: number,
): string {
  if (progress < 20) {
    return "Preparando imagen y enviando solicitud...";
  } else if (progress < 30) {
    return "Analizando la imagen del sujeto...";
  } else if (progress < 40) {
    return "Comenzando a procesar el fondo...";
  } else if (progress < 50) {
    return "Generando nuevo fondo...";
  } else if (progress < 60) {
    return "Integrando al sujeto con el nuevo fondo...";
  } else if (progress < 70) {
    return "Ajustando iluminación y sombras...";
  } else if (progress < 80) {
    return "Refinando detalles de la imagen...";
  } else if (progress < 90) {
    return "Finalizando el procesamiento...";
  } else if (progress < 100) {
    return `Esperando respuesta del servidor (intento ${attempts}/${maxAttempts})...`;
  } else {
    return "Procesamiento completado con éxito";
  }
}

/**
 * Reemplaza el fondo y reajusta la iluminación de una imagen con polling automático
 * Esta función maneja todo el proceso de inicio y consulta periódica
 * @param options Opciones para la solicitud
 * @param onProgress Callback opcional para reportar progreso con mensaje
 * @param maxAttempts Número máximo de intentos de consulta
 * @param pollingInterval Intervalo entre consultas en ms
 * @returns Promise con la URL de la imagen resultante
 */
export async function replaceBackgroundWithPolling(
  options: ReplaceBackgroundOptions,
  onProgress?: (progress: number, message?: string) => void,
  maxAttempts: number = 30,
  pollingInterval: number = 10000,
): Promise<string> {
  try {
    // Iniciar la solicitud
    const initialResponse = await replaceBackgroundAsync(options);

    if (!initialResponse.success || !initialResponse.id) {
      throw new Error(
        initialResponse.error || "No se pudo iniciar el proceso de generación",
      );
    }

    const generationId = initialResponse.id;

    // Función para esperar un tiempo determinado
    const wait = (ms: number) =>
      new Promise((resolve) => setTimeout(resolve, ms));

    // Polling para consultar el estado
    let attempts = 0;

    // Si hay progreso, iniciar desde 20%
    if (onProgress) {
      const initialMessage = getProgressMessage(20, 0, maxAttempts);
      onProgress(20, initialMessage); // Comienza en 20% para indicar que se inició el proceso
    }

    while (attempts < maxAttempts) {
      // Esperar antes de consultar
      await wait(pollingInterval);

      // Incrementar intentos
      attempts++;

      // Actualizar progreso de forma más realista
      if (onProgress) {
        // Progresión no lineal para que avance más rápido al principio y más lento después
        // Nunca supera el 85% hasta que tengamos el resultado final
        const progressMax = 85;
        const progress = Math.min(
          progressMax,
          20 + Math.pow(attempts / maxAttempts, 0.7) * 65,
        );
        const progressInt = Math.floor(progress);

        // Obtener mensaje basado en el progreso actual
        const message = getProgressMessage(progressInt, attempts, maxAttempts);

        // Enviar actualización de progreso con mensaje
        onProgress(progressInt, message);
      }

      // Consultar estado
      console.log(
        `Intento ${attempts}/${maxAttempts} - Consultando estado del ID: ${generationId}`,
      );
      const statusResponse = await checkGenerationStatus(generationId);

      console.log("📊 Status response:", statusResponse);

      // Si sigue en progreso, continuar con el polling
      if (statusResponse.status === "IN_PROGRESS") {
        console.log("⏳ Continuando polling...");
        continue;
      }

      // Si hay un error, lanzar excepción
      if (!statusResponse.success) {
        console.log("❌ Error en status response:", statusResponse.error);
        throw new Error(statusResponse.error || "Error al procesar la imagen");
      }

      // Si se completó exitosamente, devolver URL
      if (statusResponse.url) {
        console.log("🎉 URL recibida:", statusResponse.url);
        // Marcar como 100% completo con mensaje de éxito
        if (onProgress) {
          onProgress(100, "Procesamiento completado con éxito");
        }

        return statusResponse.url;
      }

      console.log("⚠️ No hay URL en la respuesta exitosa:", statusResponse);
    }

    // Si se agotaron los intentos
    throw new Error(
      "La generación está tardando demasiado. Por favor, intenta con una imagen diferente o ajusta los parámetros.",
    );
  } catch (error) {
    console.error("Error en el proceso de reemplazo de fondo:", error);
    throw error;
  }
}
