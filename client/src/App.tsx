import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { BackgroundTasksProvider } from "@/context/BackgroundTasksContext";
import { BackgroundTasksIndicator } from "@/components/BackgroundTasksIndicator";
import NotFound from "@/pages/not-found";
import Home from "@/pages/home";
import AuthPage from "@/pages/auth-page";
import LoginPage from "@/pages/login-page";
import RegisterPage from "@/pages/register-page";
import DashboardPage from "@/pages/dashboard-page";
import TestPage from "@/pages/test-page"; // Importamos la página de prueba
import TestPageNew from "@/test-page"; // Import our new test page
import TestImagePage from "@/pages/test-image-page"; // Importamos la página de prueba para imágenes
import MarketingToolsPage from "@/pages/marketing-tools-page";
import ToolPage from "@/pages/tool-page";
import AIContentHubPage from "@/pages/ai-content-hub-page";
import AIContentHubPageNew from "@/pages/ai-content-hub-page-new";
import AIContentCreatorPage from "@/pages/ai-content-creator-page-new";
import AIContentGeneratorPage from "@/pages/ai-content-generator-page";
import LangFlowDemoPage from "@/pages/langflow-demo-page";
import InstagramCopywriterPage from "@/pages/instagram-copywriter-page";
import SEOXAnalyzerPage from "@/pages/seox-analyzer-page";
import UserProfilePage from "@/pages/user-profile-page";
// Removed unused page imports
import MoodBoardPage from "@/pages/mood-board-page";
// Importamos solo la versión actualizada de Emma Studio
import EmmaVisualStudioPage from "@/pages/emma-visual-studio-v2";
import BlazeEditorNew from "@/pages/blaze-editor-new";
import SocialPostGeneratorPage from "@/pages/social-post-generator-page";
import DirectPostGeneratorPage from "@/pages/direct-post-generator";
import StandalonePostGenerator from "@/pages/standalone-post-generator";
import VisualEditorPage from "@/pages/visual-editor";
import VideoEditorPage from "@/pages/video-editor-page";
// Removed unused page imports
import SimpleVideoGeneratorPage from "@/pages/simple-video-generator-page";
import ImageToVideoPage from "@/pages/image-to-video-page";
import VideoToolsHub from "@/pages/video-tools-hub";
import VisualToolsHub from "@/pages/visual-tools-hub";
import ImageGeneratorPage from "@/pages/image-generator-page";
// Removed unused page imports
import ContentPlanner from "@/pages/content-planner";
import EditorProfesional from "@/pages/editor-profesional";
import AIImageEditor from "@/pages/ai-image-editor";
import AIImageEditorSimple from "@/pages/ai-image-editor-simple";
// Removed unused page imports - archivos eliminados
import BackgroundRemoverPage from "@/pages/background-remover-page";
import ReplaceBackgroundPage from "@/pages/replace-background-page";
import SketchToImagePage from "@/pages/sketch-to-image-improved";
import GenerationStatusTestPage from "@/pages/generation-status-test-page";
import { LangChainFormPage } from "@/pages/langchain-form-page";
import AgentTeamPage from "@/pages/agent-team-page";
import HumanServicesPageNew from "@/pages/human-services-page-new";
import MarwickLanding from "@/pages/marwick-landing";
import MarwickLandingV2 from "@/pages/marwick-landing-v2";
import EmmaTeamV2 from "@/pages/emma-team-v2";
import { AuthProvider } from "@/hooks/use-auth";
import VideoGenerator from "@/pages/video-generator"; // Importamos el componente VideoGenerator
import VibeDashboardPage from "@/pages/vibe-dashboard";
import VibeAgentsPage from "@/pages/vibe-agents";
import VibeAgentsSimple from "@/pages/vibe-agents-simple";
import TestAgentsPage from "@/pages/test-agents-page";
import ProfesionalesIA from "@/pages/profesionales-ia";
import SolucionesNegocio from "@/pages/soluciones-negocio";
import ConversationSimulatorPage from "@/pages/conversation-simulator-page";
import EmmaAgenticSeekPage from "@/pages/emma-agenticseek-page";

function Router() {
  return (
    <Switch>
      {/* Landing marketing */}
      <Route path="/" component={Home} />
      <Route path="/auth" component={AuthPage} />
      <Route path="/landing" component={Home} />
      <Route path="/dashboard" component={DashboardPage} />
      <Route path="/test-page" component={TestPage} />
      <Route path="/test-new" component={TestPageNew} />
      <Route path="/test-image" component={TestImagePage} />
      <Route
        path="/dashboard/herramientas-marketing"
        component={MarketingToolsPage}
      />
      <Route path="/dashboard/herramientas/:toolId">
        {(params) => <ToolPage toolId={params.toolId} />}
      </Route>
      {/* Nueva ruta para el simulador de conversación */}
      <Route path="/dashboard/herramientas/buyer-persona-generator/simulador/:personaId">
        {(params) => <ConversationSimulatorPage personaId={params.personaId} />}
      </Route>
      {/* Mantener ruta legacy para mood-board por compatibilidad */}
      <Route path="/dashboard/herramientas-marketing/mood-board">
        <MoodBoardPage />
      </Route>
      <Route path="/dashboard/perfil" component={UserProfilePage} />
      <Route path="/ai-content-hub" component={AIContentHubPageNew} />
      <Route path="/ai-content-hub/old" component={AIContentHubPage} />
      <Route path="/ai-content-hub/create" component={AIContentCreatorPage} />
      <Route
        path="/ai-content-generator/:subcategoryId?"
        component={AIContentGeneratorPage}
      />
      <Route path="/langflow-demo" component={LangFlowDemoPage} />
      <Route
        path="/dashboard/herramientas/instagram-copywriter"
        component={InstagramCopywriterPage}
      />
      <Route path="/instagram-copywriter" component={InstagramCopywriterPage} />
      <Route
        path="/dashboard/herramientas/seox-analyzer"
        component={SEOXAnalyzerPage}
      />
      <Route path="/seox-analyzer" component={SEOXAnalyzerPage} />
      <Route
        path="/dashboard/herramientas/social-post-generator"
        component={SocialPostGeneratorPage}
      />
      <Route path="/post-generator" component={DirectPostGeneratorPage} />
      <Route
        path="/direct-post-generator"
        component={DirectPostGeneratorPage}
      />
      <Route
        path="/standalone-post-generator"
        component={StandalonePostGenerator}
      />
      {/* UNIFICACIÓN DE EMMA STUDIO:
          Todas las rutas de Emma Studio ahora usan un único componente.
          Esto evita duplicación de código y confusión para los usuarios.
      */}
      <Route path="/emma-studio" component={EmmaVisualStudioPage} />
      <Route path="/emma-visual-studio" component={EmmaVisualStudioPage} />
      <Route path="/emma-studio/v2" component={EmmaVisualStudioPage} />
      <Route path="/test-emma-studio" component={EmmaVisualStudioPage} />
      <Route path="/emma-studio/old" component={EmmaVisualStudioPage} />
      <Route path="/emma-studio/simple" component={EmmaVisualStudioPage} />
      <Route path="/visual-editor" component={VisualEditorPage} />
      <Route path="/video-editor" component={VideoEditorPage} />
      <Route path="/video-generator" component={VideoGenerator} />
      <Route path="/video-generator-old" component={SimpleVideoGeneratorPage} />
      <Route
        path="/video-generator/simple"
        component={SimpleVideoGeneratorPage}
      />
      <Route
        path="/simple-video-generator"
        component={SimpleVideoGeneratorPage}
      />
      <Route path="/image-generator" component={ImageGeneratorPage} />
      <Route path="/image-to-video" component={ImageToVideoPage} />
      <Route path="/video-tools" component={VideoToolsHub} />
      <Route path="/visual-tools" component={VisualToolsHub} />
      <Route path="/editor-profesional" component={EditorProfesional} />
      <Route path="/ai-image-editor" component={AIImageEditor} />
      <Route path="/ai-image-editor-simple" component={AIImageEditorSimple} />
      <Route path="/blaze-editor" component={BlazeEditorNew} />
      <Route path="/background-remover" component={BackgroundRemoverPage} />
      {/* Agregamos la nueva ruta para reemplazar fondos e iluminación (modificada para debugging) */}
      <Route path="/replace-background" component={ReplaceBackgroundPage} />
      {/* Ruta para la nueva herramienta de sketch-to-image */}
      <Route path="/sketch-to-image" component={SketchToImagePage} />
      {/* Ruta para la página de verificación de estado de generaciones */}
      <Route path="/generation-status" component={GenerationStatusTestPage} />
      <Route path="/content-planner" component={ContentPlanner} />
      <Route path="/formulario/:contentTypeId">
        {(params) => <LangChainFormPage contentTypeId={params.contentTypeId} />}
      </Route>
      <Route path="/agentes/:teamId">
        {(params) => <AgentTeamPage teamId={params.teamId} />}
      </Route>
      <Route path="/dashboard/servicios" component={HumanServicesPageNew} />
      <Route path="/marwick" component={MarwickLandingV2} />
      <Route path="/marwick/old" component={MarwickLanding} />
      <Route path="/emma-team" component={EmmaTeamV2} />
      <Route path="/vibe-dashboard" component={VibeDashboardPage} />
      <Route path="/test-agents" component={TestAgentsPage} />
      <Route path="/vibe-agents" component={VibeAgentsPage} />
      <Route path="/profesionales-ia" component={ProfesionalesIA} />
      <Route path="/soluciones-negocio" component={SolucionesNegocio} />
      <Route path="/agent-demo" component={VibeAgentsPage} /> {/* Updated to use the modern agent implementation */}
      {/*
        🤖 EMMA AI - AGENTE PRINCIPAL DE EMMA STUDIO

        Emma AI es el agente revolucionario construido sobre AgenticSeek (open source)
        con funcionalidades cloud personalizadas. Coordina múltiples agentes especializados
        para reemplazar completamente las agencias de marketing.

        RUTAS DISPONIBLES:
        - /emma-ai (PRINCIPAL - usar esta para acceso normal)
        - /emma-agenticseek (técnica - para desarrolladores)
        - /dashboard/emma-ai (desde dashboard)

        ARQUITECTURA:
        - Frontend: React component integrado en Emma Studio
        - Backend: AgenticSeek + Emma Orchestrator (puerto 8001)
        - Agentes: CasualAgent, BrowserAgent, CoderAgent, PlannerAgent, FileAgent, McpAgent
        - Cloud: Browserless.io, Serper, Gemini APIs

        DOCUMENTACIÓN: client/src/pages/EMMA_AI_README.md
      */}
      <Route path="/emma-agenticseek" component={EmmaAgenticSeekPage} />
      <Route path="/emma-ai" component={EmmaAgenticSeekPage} />
      <Route path="/dashboard/emma-ai" component={EmmaAgenticSeekPage} />
      {/* Mantener esta ruta para otras páginas del dashboard que aún no tienen componentes específicos */}
      <Route path="/dashboard/:path*" component={DashboardPage} />
      <Route path="/login" component={LoginPage} />
      <Route path="/register" component={RegisterPage} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <BackgroundTasksProvider>
          <Router />
          {/* Agregar el indicador de tareas en segundo plano en la esquina superior derecha */}
          <div className="fixed top-4 right-4 z-50">
            <BackgroundTasksIndicator />
          </div>
          <Toaster />
        </BackgroundTasksProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
