"""Schemas for buyer persona generation."""

from typing import List, Optional, Dict, Any, Literal
from pydantic import BaseModel, Field, validator, root_validator
from enum import Enum


class ProductType(str, Enum):
    """Enum for product types."""
    PHYSICAL = "physical"
    SOFTWARE = "software"
    SERVICE = "service"
    COURSE = "course"
    CONTENT = "content"


class GeographicScope(str, Enum):
    """Enum for geographic scope."""
    LOCAL = "local"
    NATIONAL = "national"
    INTERNATIONAL = "international"
    ONLINE = "online"


class PriceRange(str, Enum):
    """Enum for price ranges."""
    FREE = "free"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    ENTERPRISE = "enterprise"


class UrgencyLevel(str, Enum):
    """Enum for problem urgency levels."""
    CRITICAL = "critical"
    IMPORTANT = "important"
    NICE = "nice"
    FUTURE = "future"


class AudienceKnowledge(str, Enum):
    """Enum for audience knowledge levels."""
    CLEAR = "clear"
    GENERAL = "general"
    UNSURE = "unsure"
    EXPLORE = "explore"


class SmartFormData(BaseModel):
    """Enhanced form data schema matching frontend SmartFormData."""
    product_type: ProductType = Field(..., description="Type of product or service")
    product_name: str = Field(..., min_length=1, max_length=200, description="Name of the product/service")
    industry: str = Field(..., min_length=1, max_length=100, description="Industry sector")
    target_audience: str = Field(..., min_length=10, max_length=1000, description="Target audience description")
    main_problem: str = Field(..., min_length=10, max_length=1000, description="Main problem being solved")
    price_range: PriceRange = Field(..., description="Price range category")
    unique_value: Optional[str] = Field(None, max_length=500, description="Unique value proposition")

    # Geographic and market data
    geographic_scope: GeographicScope = Field(..., description="Geographic reach")
    target_country: str = Field(..., min_length=2, max_length=100, description="Primary target country")
    target_regions: List[str] = Field(default_factory=list, description="Specific regions if applicable")
    sales_channels: List[str] = Field(..., min_items=1, description="Sales channels used")

    # Audience insights
    audience_knowledge: AudienceKnowledge = Field(..., description="Level of audience knowledge")
    existing_audience: Optional[str] = Field(None, max_length=1000, description="Current audience description")
    business_sizes: List[str] = Field(..., min_items=1, description="Target business sizes")

    # Problem and decision context
    problem_urgency: UrgencyLevel = Field(..., description="Urgency level of the problem")
    decision_maker: str = Field(..., min_length=1, description="Who makes the buying decision")

    @validator('sales_channels', 'business_sizes')
    def validate_non_empty_lists(cls, v):
        """Ensure lists are not empty when required."""
        if not v:
            raise ValueError("This field cannot be empty")
        return v

    @validator('target_country')
    def validate_country_code(cls, v):
        """Validate country code format."""
        if len(v) < 2:
            raise ValueError("Country code must be at least 2 characters")
        return v.upper()


class BuyerPersonaRequest(BaseModel):
    """Enhanced request schema for buyer persona generation."""
    # Legacy support for simple description
    product_description: Optional[str] = Field(None, min_length=10, max_length=2000, description="Detailed product description")

    # Enhanced smart form data
    smart_form_data: Optional[SmartFormData] = Field(None, description="Structured form data")

    # Optional legacy fields
    industry: Optional[str] = Field(None, max_length=100, description="Industry or sector")
    target_market: Optional[str] = Field(None, max_length=500, description="Target market description")
    num_personas: int = Field(3, ge=1, le=5, description="Number of personas to generate (1-5)")
    business_goals: Optional[str] = Field(None, max_length=1000, description="Business goals and objectives")
    competitors: Optional[str] = Field(None, max_length=500, description="Main competitors")

    @root_validator(skip_on_failure=True)
    @classmethod
    def validate_request_data(cls, values):
        """Ensure either product_description or smart_form_data is provided."""
        if isinstance(values, dict):
            if not values.get('smart_form_data') and not values.get('product_description'):
                raise ValueError("Either product_description or smart_form_data must be provided")
        return values


class JobInfo(BaseModel):
    """Job information for a buyer persona."""
    title: str = Field(..., min_length=1, max_length=200, description="Job title")
    company_size: str = Field(..., min_length=1, max_length=100, description="Company size category")
    industry: str = Field(..., min_length=1, max_length=100, description="Industry sector")
    responsibilities: List[str] = Field(..., min_items=1, max_items=10, description="Key job responsibilities")

    @validator('responsibilities')
    def validate_responsibilities(cls, v):
        """Validate responsibilities list."""
        if not v:
            raise ValueError("At least one responsibility is required")
        return [resp.strip() for resp in v if resp.strip()]


class BuyingProcess(BaseModel):
    """Buying process information."""
    research_methods: List[str] = Field(..., min_items=1, max_items=10, description="How they research solutions")
    decision_factors: List[str] = Field(..., min_items=1, max_items=10, description="Key decision factors")
    timeline: str = Field(..., min_length=1, max_length=200, description="Typical buying timeline")

    @validator('research_methods', 'decision_factors')
    def validate_process_lists(cls, v):
        """Validate process-related lists."""
        if not v:
            raise ValueError("At least one item is required")
        return [item.strip() for item in v if item.strip()]


class BuyerPersona(BaseModel):
    """Complete buyer persona profile."""
    name: str = Field(..., min_length=2, max_length=100, description="Full name")
    age: int = Field(..., ge=18, le=100, description="Age in years")
    gender: str = Field(..., min_length=1, max_length=50, description="Gender identity")
    location: str = Field(..., min_length=2, max_length=200, description="Geographic location")
    education: str = Field(..., min_length=1, max_length=200, description="Education level")
    income_level: str = Field(..., min_length=1, max_length=100, description="Income bracket")
    marital_status: str = Field(..., min_length=1, max_length=50, description="Marital status")
    job: JobInfo = Field(..., description="Job information")
    personal_background: str = Field(..., min_length=10, max_length=1000, description="Personal background")
    goals: List[str] = Field(..., min_items=1, max_items=10, description="Personal and professional goals")
    challenges: List[str] = Field(..., min_items=1, max_items=10, description="Main challenges and pain points")
    buying_process: BuyingProcess = Field(..., description="Buying process information")
    objections: List[str] = Field(..., min_items=1, max_items=10, description="Common objections")
    communication_channels: List[str] = Field(..., min_items=1, max_items=10, description="Preferred communication channels")
    influences: List[str] = Field(..., min_items=1, max_items=10, description="Key influences and decision factors")
    quotes: List[str] = Field(..., min_items=1, max_items=5, description="Representative quotes")
    typical_day: str = Field(..., min_length=50, max_length=1000, description="Description of a typical day")
    brand_affinities: List[str] = Field(..., min_items=1, max_items=15, description="Preferred brands and products")
    avatar_description: str = Field(..., min_length=20, max_length=500, description="Physical description for avatar generation")

    @validator('goals', 'challenges', 'objections', 'communication_channels', 'influences', 'quotes', 'brand_affinities')
    def validate_persona_lists(cls, v):
        """Validate persona-related lists."""
        if not v:
            raise ValueError("At least one item is required")
        return [item.strip() for item in v if item.strip()]

    @validator('name')
    def validate_name(cls, v):
        """Validate name format."""
        if not v.strip():
            raise ValueError("Name cannot be empty")
        # Check for realistic name (at least first and last name)
        parts = v.strip().split()
        if len(parts) < 2:
            raise ValueError("Name should include at least first and last name")
        return v.strip()

    @validator('age')
    def validate_age_range(cls, v):
        """Validate age is in reasonable range."""
        if v < 18 or v > 100:
            raise ValueError("Age must be between 18 and 100")
        return v


class MarketingRecommendation(BaseModel):
    """Marketing recommendations for a persona."""
    persona_name: str = Field(..., min_length=2, max_length=100, description="Name of the persona")
    content_types: List[str] = Field(..., min_items=1, max_items=10, description="Recommended content types")
    messaging_tips: List[str] = Field(..., min_items=1, max_items=10, description="Messaging strategies")
    channels: List[str] = Field(..., min_items=1, max_items=10, description="Recommended marketing channels")
    content_topics: List[str] = Field(..., min_items=1, max_items=15, description="Relevant content topics")

    @validator('content_types', 'messaging_tips', 'channels', 'content_topics')
    def validate_recommendation_lists(cls, v):
        """Validate recommendation lists."""
        if not v:
            raise ValueError("At least one item is required")
        return [item.strip() for item in v if item.strip()]


class BuyerPersonaResponse(BaseModel):
    """Enhanced response schema for buyer persona generation."""
    status: Literal["success", "error", "partial"] = Field(default="success", description="Response status")
    buyer_personas: List[BuyerPersona] = Field(default_factory=list, description="Generated buyer personas")
    marketing_recommendations: List[MarketingRecommendation] = Field(default_factory=list, description="Marketing recommendations")
    general_insights: List[str] = Field(default_factory=list, description="General market insights")
    timestamp: str = Field(..., description="Generation timestamp")
    request_id: str = Field(..., description="Unique request identifier")
    error_message: Optional[str] = Field(None, description="Error message if applicable")

    # Enhanced metadata
    generation_time_seconds: Optional[float] = Field(None, description="Time taken to generate")
    ai_model_used: Optional[str] = Field(None, description="AI model used for generation")
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Confidence in results")

    @root_validator
    @classmethod
    def validate_personas_count(cls, values):
        """Validate personas count matches request."""
        if values.get('status') == 'success' and not values.get('buyer_personas'):
            raise ValueError("Success status requires at least one persona")
        return values

    @validator('general_insights')
    def validate_insights(cls, v):
        """Validate insights are meaningful."""
        if v:
            return [insight.strip() for insight in v if insight.strip() and len(insight.strip()) > 10]
        return v


class TextAssistRequest(BaseModel):
    """Enhanced request schema for text assistance."""
    content: str = Field(..., min_length=1, max_length=5000, description="Text content to improve")
    context: Optional[str] = Field(None, max_length=500, description="Context for improvement")
    improvement_type: Optional[Literal["clarity", "marketing", "professional", "persuasive"]] = Field(
        default="marketing", description="Type of improvement needed"
    )
    target_audience: Optional[str] = Field(None, max_length=200, description="Target audience for the text")

    @validator('content')
    def validate_content(cls, v):
        """Validate content is meaningful."""
        content = v.strip()
        if not content:
            raise ValueError("Content cannot be empty")
        if len(content) < 5:
            raise ValueError("Content must be at least 5 characters long")
        return content


class TextAssistResponse(BaseModel):
    """Enhanced response schema for text assistance."""
    status: Literal["success", "error"] = Field(default="success", description="Response status")
    suggestions: List[str] = Field(default_factory=list, description="Improvement suggestions")
    original_content: str = Field(..., description="Original text content")
    improved_content: Optional[str] = Field(None, description="AI-improved version")
    improvement_notes: List[str] = Field(default_factory=list, description="Notes about improvements made")
    error_message: Optional[str] = Field(None, description="Error message if applicable")

    @root_validator
    @classmethod
    def validate_suggestions(cls, values):
        """Validate suggestions are provided for success status."""
        if values.get('status') == 'success' and not values.get('suggestions'):
            raise ValueError("Success status requires at least one suggestion")
        suggestions = values.get('suggestions', [])
        values['suggestions'] = [suggestion.strip() for suggestion in suggestions if suggestion.strip()]
        return values
