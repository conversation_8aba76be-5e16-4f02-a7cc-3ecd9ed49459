* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #ffffff; /* Pure white background for Emma Studio consistency */
  color: #1e293b; /* Dark text */
  overflow-x: hidden;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.header {
  padding: 20px 24px;
  background: #ffffff; /* Clean white header */
  border-bottom: 1px solid #e5e7eb; /* Subtle border */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* Minimal shadow */
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.emma-branding {
  display: flex;
  align-items: center;
  gap: 16px;
}

.emma-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 2px solid #e5e7eb; /* Subtle border */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08); /* Softer shadow */
}

.emma-info h1 {
  font-size: 1.8rem;
  font-weight: 600; /* Slightly lighter weight */
  color: #1e293b;
  margin: 0;
  text-shadow: none;
}

.emma-info p {
  font-size: 0.9rem;
  color: #6b7280; /* Softer gray */
  margin: 2px 0 0 0;
  font-weight: 400;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f9fafb; /* Light gray background */
  padding: 8px 16px;
  border-radius: 20px;
  color: #374151; /* Darker text for better contrast */
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid #e5e7eb; /* Subtle border */
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-dot.online {
  background-color: #10b981;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
}

.status-dot.offline {
  background-color: #ef4444;
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.6);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.section-tabs {
  display: flex;
  gap: 8px;
  width: 100%;
  max-width: 800px;
  justify-content: center;
}

.section-tabs button {
  padding: 10px 20px;
  background-color: #f9fafb; /* Light background */
  color: #374151; /* Dark text */
  border: 1px solid #e5e7eb; /* Subtle border */
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.section-tabs button.active {
  background-color: #3b82f6; /* Modern blue */
  color: #ffffff; /* white */
  border-color: #3b82f6;
}

.section-tabs button:hover:not(.active) {
  background-color: #f3f4f6; /* Light hover */
  color: #1f2937; /* Darker text */
  border-color: #d1d5db;
}

.main {
  flex: 1;
  padding: 16px;
  width: 100%;
}

.app-sections {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  height: calc(100vh - 80px);
}

.left-panel,
.right-panel {
  background-color: #ffffff; /* Pure white */
  border: 1px solid #e5e7eb; /* Subtle border */
  border-radius: 12px; /* Slightly more rounded */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* Minimal shadow */
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.left-panel {
  padding: 0;
  display: flex;
  flex-direction: column;
}

.task-section,
.chat-section,
.computer-section {
  background-color: #ffffff; /* lightCard */
  border: 1px solid #e2e8f0; /* lightBorder */
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.task-section h2,
.chat-section h2,
.computer-section h2 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #475569; /* lightTextSecondary */
  margin-bottom: 12px;
  letter-spacing: 0.5px;
  border-bottom: 1px solid #e2e8f0; /* lightBorder */
  padding-bottom: 8px;
}

.task-details {
  flex: 1;
  overflow-y: auto;
  background-color: #f9fafb; /* Light background */
  border-radius: 8px;
  padding: 16px;
  margin-top: 12px;
  border: 1px solid #e5e7eb;
}

.screenshot-container {
  flex: 1;
  overflow: auto;
  margin-top: 12px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  background-color: #f9fafb; /* Light background */
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e5e7eb;
}

.screenshot-container img {
  max-width: 100%;
  border: 1px solid #4a5568; /* Medium gray */
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.left-panel h2,
.right-panel h2 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #cbd5e1; /* darkTextSecondary */
  margin-bottom: 8px;
  letter-spacing: 1px;
}

.messages {
  flex: 1;
  overflow-y: auto;
  padding: 12px 8px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 8px;
}

/* Message header layout */
.message-header {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.reasoning-toggle {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    color: #fff;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    align-self: flex-start;
}

.reasoning-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.reasoning-toggle:active {
    transform: translateY(1px);
}

/* Reasoning content container */
.reasoning-content {
    margin-top: 12px;
    padding: 12px;
    background: rgba(0, 0, 0, 0.2);
    border-left: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 0 4px 4px 0;
    font-size: 0.9em;
    line-height: 1.4;
}

.reasoning-content h1,
.reasoning-content h2,
.reasoning-content h3,
.reasoning-content h4,
.reasoning-content h5,
.reasoning-content h6 {
    font-size: 1em;
    margin: 8px 0 4px 0;
    color: rgba(255, 255, 255, 0.9);
}

.reasoning-content p {
    margin: 6px 0;
    color: rgba(255, 255, 255, 0.8);
}

/* Alternative light theme styles */
.message.user-message .reasoning-toggle {
    background: rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.1);
    color: #333;
}

.message.user-message .reasoning-toggle:hover {
    background: rgba(0, 0, 0, 0.1);
    border-color: rgba(0, 0, 0, 0.2);
}

.message.user-message .reasoning-content {
    background: rgba(0, 0, 0, 0.03);
    border-left-color: rgba(0, 0, 0, 0.2);
}

.message.user-message .reasoning-content p {
    color: rgba(0, 0, 0, 0.7);
}

.welcome-message {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 32px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  border: 1px solid #e5e7eb;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.welcome-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 2px solid #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.welcome-content h3 {
  color: #1f2937;
  font-size: 1.6rem;
  font-weight: 700;
  margin: 0 0 16px 0;
}

.welcome-content p {
  color: #4b5563;
  font-size: 1rem;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.welcome-content ul {
  list-style: none;
  padding: 0;
  margin: 16px 0;
}

.welcome-content li {
  color: #374151;
  font-size: 0.95rem;
  margin: 8px 0;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.welcome-cta {
  color: #1f2937 !important;
  font-weight: 600 !important;
  font-size: 1.2rem !important;
  margin: 24px 0 0 0 !important;
}

.placeholder {
  text-align: center;
  color: #64748b; /* lighter gray */
  margin-top: 20px;
  font-style: italic;
}

.message {
  max-width: 85%;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 0.95rem;
  line-height: 1.5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.messages::-webkit-scrollbar,
.content::-webkit-scrollbar {
  width: 6px;
}

.messages::-webkit-scrollbar-track,
.content::-webkit-scrollbar-track {
  background: #2d3748; /* Slightly lighter than darkCard */
  border-radius: 8px;
}

.messages::-webkit-scrollbar-thumb,
.content::-webkit-scrollbar-thumb {
  background: #4a5568; /* Medium gray */
  border-radius: 8px;
}

.messages::-webkit-scrollbar-thumb:hover,
.content::-webkit-scrollbar-thumb:hover {
  background: #718096; /* Lighter gray on hover */
}

.user-message {
  background-color: #3b82f6; /* Modern blue */
  color: #ffffff; /* white */
  align-self: flex-end;
  border-top-right-radius: 4px;
}

.agent-message {
  background-color: #f8fafc; /* Very light background */
  color: #1f2937; /* Dark text */
  align-self: flex-start;
  border-top-left-radius: 4px;
  border: 1px solid #e5e7eb; /* Subtle border */
}

.error-message {
  background-color: #ef4444; /* Modern red */
  color: #ffffff; /* white */
  align-self: flex-start;
  border-top-left-radius: 4px;
}

.agent-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.message-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid rgba(226, 232, 240, 0.5);
  flex-shrink: 0;
}

.agent-name {
  font-size: 0.85rem;
  color: #475569;
  font-weight: 600;
  margin: 0;
}

.loading-animation {
  text-align: center;
  color: #cbd5e1; /* darkTextSecondary */
  padding: 8px 0;
  font-size: 0.9rem;
  font-style: italic;
  border-top: 1px solid #334155; /* darkBorder */
  margin-bottom: 8px;
}

.input-form {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.input-form input {
  flex: 1;
  padding: 12px 16px;
  font-size: 0.95rem;
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  color: #1f2937;
  border-radius: 8px;
  outline: none;
  transition: all 0.2s ease;
}

.input-form input:focus {
  border-color: #3b82f6; /* Modern blue */
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); /* Blue glow */
}

.input-form button {
  padding: 12px 20px;
  font-size: 0.95rem;
  background-color: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.input-form button:hover {
  background-color: #2563eb; /* Darker blue on hover */
  transform: translateY(-1px); /* Subtle lift effect */
}

.input-form button:disabled {
  background-color: #9ca3af; /* Light gray */
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.right-panel {
  padding: 16px;
}

.view-selector {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.view-selector button {
  padding: 10px 16px;
  font-size: 0.9rem;
  background-color: #ffffff; /* White background */
  color: #374151; /* Dark text */
  border: 1px solid #e5e7eb; /* Light border */
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.view-selector button.active {
  background-color: #3b82f6; /* Modern blue */
  color: #ffffff; /* white */
  border-color: #3b82f6; /* Modern blue */
}

.view-selector button:hover:not(.active) {
  background-color: #f3f4f6; /* Light hover */
  color: #1f2937; /* Darker text */
  border-color: #d1d5db;
}

.view-selector button:disabled {
  background-color: #f9fafb; /* Light gray */
  opacity: 0.5;
  cursor: not-allowed;
}

.content {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
  margin-top: 8px;
}

.blocks {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.block {
  background-color: #ffffff; /* White background */
  padding: 16px;
  border: 1px solid #e5e7eb; /* Light border */
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* Minimal shadow */
}

.block-tool,
.block-feedback,
.block-success {
  font-size: 0.9rem;
  margin-bottom: 8px;
  color: #374151; /* Dark text */
}

.block-tool {
  font-weight: 600;
  color: #3b82f6; /* Modern blue */
}

.block-success {
  color: #10b981; /* Modern green */
}

.block-failure {
  color: #ef4444; /* Modern red */
}

.block pre {
  background-color: #f8fafc; /* Light background */
  padding: 12px;
  border-radius: 6px;
  font-size: 0.85rem;
  white-space: pre-wrap;
  word-break: break-all;
  color: #1f2937; /* Dark text */
  margin: 8px 0;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  border: 1px solid #e5e7eb; /* Light border */
}

.screenshot {
  margin-top: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.screenshot img {
  max-width: 100%;
  border: 1px solid #4a5568; /* Medium gray */
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.error {
  color: #dc3545; /* error */
  font-size: 0.9rem;
  margin-bottom: 12px;
  padding: 8px 12px;
  background-color: rgba(220, 53, 69, 0.1); /* error with opacity */
  border-radius: 6px;
  border-left: 3px solid #dc3545; /* error */
}

@media (max-width: 1024px) {
  .app-sections {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto 1fr;
  }

  .task-section {
    grid-column: 1 / -1;
  }
}

@media (max-width: 768px) {
  .main {
    padding: 16px;
  }

  .app-sections {
    grid-template-columns: 1fr;
    height: auto;
    gap: 16px;
  }

  .task-section,
  .chat-section,
  .computer-section {
    height: calc(33vh - 60px);
    min-height: 300px;
  }

  .header h1 {
    font-size: 1.5rem;
  }

  .input-form button {
    padding: 12px 16px;
  }
}

@media (max-width: 480px) {
  .main {
    padding: 12px;
  }

  .message {
    max-width: 90%;
    padding: 10px 12px;
  }

  .view-selector button {
    padding: 8px 12px;
    font-size: 0.85rem;
  }

  .task-section,
  .chat-section,
  .computer-section {
    padding: 12px;
  }

  .input-form {
    margin-top: 8px;
  }

  .header h1 {
    font-size: 1.3rem;
  }

  .task-section h2,
  .chat-section h2,
  .computer-section h2 {
    font-size: 1rem;
    margin-bottom: 8px;
    padding-bottom: 6px;
  }
}

/* Sources styling */
.sources {
    margin-top: 15px;
    padding: 12px;
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 3px solid #28a745;
    border-radius: 0 4px 4px 0;
}

.sources h4 {
    margin: 0 0 8px 0;
    color: #28a745;
    font-size: 0.9em;
    font-weight: 600;
}

.sources ul {
    margin: 0;
    padding-left: 20px;
}

.sources li {
    margin-bottom: 5px;
}

.sources a {
    color: #66d9ef;
    text-decoration: none;
    font-size: 0.85em;
}

.sources a:hover {
    text-decoration: underline;
    color: #a6e3ff;
}

/* Agent Status Styles */
.agent-status {
    padding: 20px;
    color: #e2e8f0;
    height: 100%;
    overflow-y: auto;
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #4a5568;
}

.status-header h3 {
    margin: 0;
    color: #66d9ef;
    font-size: 1.2rem;
}

.status-indicator {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.status-indicator.online {
    background-color: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid #28a745;
}

.status-indicator.offline {
    background-color: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border: 1px solid #dc3545;
}

.current-activity {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #2d3748;
    border-radius: 8px;
    border-left: 4px solid #66d9ef;
}

.current-activity h4 {
    margin: 0 0 10px 0;
    color: #66d9ef;
    font-size: 1rem;
}

.activity-text {
    margin: 0;
    color: #cbd5e1;
    font-size: 0.9rem;
}

.loading-spinner {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
    color: #66d9ef;
    font-size: 0.85rem;
}

.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #4a5568;
    border-top: 2px solid #66d9ef;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.agent-info {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #2d3748;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.agent-info h4 {
    margin: 0 0 15px 0;
    color: #28a745;
    font-size: 1rem;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #4a5568;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item .label {
    color: #a0aec0;
    font-size: 0.85rem;
    font-weight: 500;
}

.info-item .value {
    color: #e2e8f0;
    font-size: 0.85rem;
    font-weight: 600;
}

.last-response {
    padding: 15px;
    background-color: #2d3748;
    border-radius: 8px;
    border-left: 4px solid #f39c12;
}

.last-response h4 {
    margin: 0 0 10px 0;
    color: #f39c12;
    font-size: 1rem;
}

.response-info p {
    margin: 5px 0;
    color: #cbd5e1;
    font-size: 0.85rem;
}

.response-info strong {
    color: #e2e8f0;
}

/* Mode Selector Styles */
.mode-selector {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-bottom: 20px;
    padding: 12px;
    background: #f9fafb; /* Light background */
    border-radius: 12px;
    border: 1px solid #e5e7eb; /* Light border */
}

.mode-btn {
    padding: 12px 24px;
    border: 1px solid #e5e7eb; /* Light border */
    border-radius: 8px;
    background: #ffffff; /* White background */
    color: #374151; /* Dark text */
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.mode-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.05), transparent);
    transition: left 0.5s;
}

.mode-btn:hover::before {
    left: 100%;
}

.mode-btn:hover {
    background: #f3f4f6; /* Light hover */
    color: #1f2937; /* Darker text */
    border-color: #d1d5db;
    transform: translateY(-1px);
}

.mode-btn.active {
    background: #3b82f6; /* Modern blue */
    color: #ffffff; /* White text */
    border-color: #3b82f6;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2); /* Blue shadow */
    transform: translateY(-1px);
}

/* Browser View Styles */
.browser-view {
    height: 100%;
    overflow-y: auto;
}

.screenshot-container {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.screenshot-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #4a5568;
}

.screenshot-header h3 {
    margin: 0;
    color: #66d9ef;
    font-size: 1.2rem;
}

.screenshot {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
    background-color: #1a202c;
    border-radius: 8px;
    padding: 10px;
}

.no-screenshot {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #2d3748;
    border-radius: 8px;
    border: 2px dashed #4a5568;
    margin: 20px 0;
}

.placeholder-content {
    text-align: center;
    color: #cbd5e1;
    padding: 40px 20px;
}

.placeholder-content h4 {
    margin: 0 0 15px 0;
    color: #66d9ef;
    font-size: 1.1rem;
}

.placeholder-content p {
    margin: 0 0 20px 0;
    color: #a0aec0;
    font-size: 0.9rem;
    line-height: 1.5;
}

.browser-info {
    margin-top: 20px;
    padding: 15px;
    background-color: #2d3748;
    border-radius: 8px;
    border-left: 4px solid #66d9ef;
}

.browser-info h4 {
    margin: 0 0 15px 0;
    color: #66d9ef;
    font-size: 1rem;
}

/* Browser Activity Styles */
.browser-activity {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    background-color: #1a202c;
    border-radius: 8px;
    margin: 20px 0;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #4a5568;
}

.activity-header h4 {
    margin: 0;
    color: #66d9ef;
    font-size: 1.1rem;
}

.activity-log {
    flex: 1;
    overflow-y: auto;
    max-height: 300px;
}

.activity-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
    padding: 10px;
    margin-bottom: 10px;
    background-color: #2d3748;
    border-radius: 6px;
    border-left: 3px solid #4a5568;
}

.activity-item.query {
    border-left-color: #66d9ef;
}

.activity-item.search {
    border-left-color: #28a745;
}

.activity-item.browser {
    border-left-color: #f39c12;
}

.activity-time {
    font-size: 0.75rem;
    color: #a0aec0;
    font-weight: 500;
}

.activity-content {
    font-size: 0.85rem;
    color: #e2e8f0;
    line-height: 1.4;
}

.no-activity {
    text-align: center;
    padding: 40px 20px;
    color: #cbd5e1;
}

.no-activity p {
    margin: 0 0 15px 0;
    color: #a0aec0;
    font-size: 0.9rem;
}

.no-activity ul {
    text-align: left;
    max-width: 300px;
    margin: 20px auto 0;
    padding: 0;
    list-style: none;
}

.no-activity li {
    padding: 5px 0;
    color: #cbd5e1;
    font-size: 0.85rem;
}

.current-search {
    margin-top: 20px;
    padding: 15px;
    background-color: #2d3748;
    border-radius: 8px;
    border-left: 4px solid #66d9ef;
}

.current-search h5 {
    margin: 0 0 10px 0;
    color: #66d9ef;
    font-size: 0.9rem;
}

.current-search p {
    margin: 0;
    color: #e2e8f0;
    font-size: 0.85rem;
    font-style: italic;
}

@media (max-width: 768px) {
    .info-grid {
        grid-template-columns: 1fr;
    }

    .status-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }

    .mode-selector {
        flex-direction: column;
        gap: 10px;
    }

    .mode-btn {
        justify-content: center;
    }

    .screenshot-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
}

/* User-friendly workflow styles */
.workflow-view {
  padding: 16px;
}

.workflow-active h4 {
  color: #1e293b;
  margin-bottom: 16px;
  font-size: 1.1rem;
}

.workflow-steps {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.workflow-step {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.step-indicator {
  font-size: 1.2rem;
  min-width: 24px;
}

.step-content {
  flex: 1;
}

.step-title {
  font-weight: 500;
  color: #1e293b;
  margin: 0 0 4px 0;
}

.step-status {
  font-size: 0.85rem;
  color: #059669;
  margin: 0;
}

.workflow-idle {
  text-align: center;
  padding: 32px 16px;
  color: #64748b;
}

.idle-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.workflow-idle h4 {
  color: #1e293b;
  margin-bottom: 8px;
}

/* Capabilities and benefits styles */
.capabilities-list,
.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.capability-item,
.benefit-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.capability-icon,
.benefit-icon {
  font-size: 1.2rem;
  min-width: 24px;
}

.capability-text,
.benefit-text {
  color: #475569;
  font-size: 0.9rem;
}

.recent-activity h4,
.agent-capabilities h4,
.browser-benefits h4 {
  color: #1e293b;
  margin-bottom: 12px;
}

.activity-summary p {
  color: #64748b;
  margin: 4px 0;
  font-size: 0.9rem;
}

/* Emma Header Styles */
.emma-header {
  padding: 24px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 12px 12px 0 0;
  margin-bottom: 0;
}

.emma-profile {
  display: flex;
  align-items: center;
  gap: 16px;
}

.emma-avatar-container {
  position: relative;
}

.emma-avatar-real {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.emma-avatar-placeholder {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.emma-details h2 {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 4px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.emma-details p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
  margin: 0 0 8px 0;
  font-weight: 400;
}

.emma-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.95);
  font-size: 0.85rem;
  font-weight: 500;
}

/* Message Avatar Real */
.message-avatar-real {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(102, 126, 234, 0.3);
  flex-shrink: 0;
}

/* Message Avatar Placeholder */
.message-avatar-placeholder {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

/* Capabilities Grid */
.capabilities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin: 20px 0;
}

.capability-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.5);
  transition: all 0.3s ease;
}

.capability-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
}

.capability-emoji {
  font-size: 1.2rem;
  min-width: 24px;
}

.capability-card span:last-child {
  color: #1e293b;
  font-size: 0.9rem;
  font-weight: 500;
}



/* Loading and Offline Messages */
.loading-animation {
  padding: 12px 20px;
  text-align: center;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  color: #64748b;
}

.offline-message {
  padding: 12px 20px;
  text-align: center;
  background: #fef2f2;
  border-top: 1px solid #fecaca;
  color: #dc2626;
  font-size: 0.9rem;
}

/* Chat Section Layout Fix */
.chat-section {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #ffffff;
}

/* Welcome Message Improvements */
.welcome-message {
  padding: 32px 24px;
  text-align: center;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.5);
  margin-bottom: 20px;
}

.welcome-content h3 {
  color: #1e293b;
  font-size: 1.6rem;
  font-weight: 700;
  margin: 0 0 16px 0;
}

.welcome-content p {
  color: #475569;
  font-size: 1.1rem;
  margin: 0 0 24px 0;
  line-height: 1.6;
}

.welcome-cta {
  color: #1e293b !important;
  font-weight: 600 !important;
  font-size: 1.2rem !important;
  margin: 24px 0 0 0 !important;
}



/* Emma Status Section */
.emma-status-section {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.emma-status-section .status-header {
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.emma-status-section .status-header h2 {
  margin: 0;
  color: #1e293b;
  font-size: 1.2rem;
  font-weight: 600;
}

.mode-info {
  padding: 20px;
}

.current-mode h3 {
  color: #1e293b;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.mode-display {
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e2e8f0;
}

.mode-active {
  display: flex;
  align-items: center;
  gap: 12px;
}

.mode-icon {
  font-size: 1.5rem;
  min-width: 32px;
}

.mode-active strong {
  color: #1e293b;
  font-size: 0.95rem;
  margin: 0 0 4px 0;
  display: block;
}

.mode-active p {
  color: #64748b;
  font-size: 0.85rem;
  margin: 0;
}

/* Emma Activity Section */
.emma-activity {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.error-display {
  background: #fef2f2;
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #fecaca;
  margin-bottom: 16px;
  font-size: 0.9rem;
}

.activity-working {
  text-align: center;
  padding: 32px 16px;
}

.working-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.working-animation h4 {
  color: #1e293b;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.working-steps {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 250px;
  margin: 0 auto;
}

.step {
  padding: 8px 12px;
  background: #f1f5f9;
  border-radius: 6px;
  font-size: 0.85rem;
  color: #64748b;
  transition: all 0.3s ease;
}

.step.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateX(4px);
}

.activity-idle {
  text-align: center;
  padding: 24px 16px;
}

.idle-display {
  margin-bottom: 32px;
}

.idle-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.idle-display h4 {
  color: #1e293b;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.idle-display p {
  color: #64748b;
  font-size: 0.9rem;
  margin: 0;
}

.emma-capabilities h4 {
  color: #1e293b;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 16px 0;
  text-align: left;
}

.emma-capabilities .capabilities-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  text-align: left;
}

.capability {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.capability span:first-child {
  font-size: 1.1rem;
  min-width: 24px;
}

.capability span:last-child {
  color: #475569;
  font-size: 0.9rem;
}

.screenshot-preview {
  margin-top: 20px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.screenshot-preview h4 {
  color: #1e293b;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.screenshot-img {
  width: 100%;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .emma-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
    height: auto;
    gap: 16px;
  }

  .emma-status-section {
    order: 2;
  }

  .chat-section {
    order: 1;
    min-height: 60vh;
  }
}

@media (max-width: 768px) {
  .emma-container {
    gap: 12px;
    padding: 0 12px;
  }

  .emma-header {
    padding: 16px;
  }

  .emma-avatar-placeholder {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }

  .emma-details h2 {
    font-size: 1.3rem;
  }

  .capabilities-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .mode-info,
  .emma-activity {
    padding: 16px;
  }
}