import React, { useState, useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import axios from 'axios';
import './App.css';

function App() {
    const [query, setQuery] = useState('');
    const [messages, setMessages] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [currentView, setCurrentView] = useState('blocks');
    const [responseData, setResponseData] = useState(null);
    const [isOnline, setIsOnline] = useState(false);
    const [status, setStatus] = useState('Agents ready');
    const [expandedReasoning, setExpandedReasoning] = useState(new Set());
  const [searchActivity, setSearchActivity] = useState([]);
  const [currentSearch, setCurrentSearch] = useState(null);
  const [browserMode, setBrowserMode] = useState(false); // false = cloud, true = browser
  const [screenshotUrl, setScreenshotUrl] = useState(null);
    const messagesEndRef = useRef(null);

    useEffect(() => {
        const intervalId = setInterval(() => {
            checkHealth();
            if (browserMode) {
                fetchScreenshot(); // Fetch screenshots only in browser mode
            }
        }, 3000);
        return () => clearInterval(intervalId);
    }, [messages, browserMode]);

    const checkHealth = async () => {
        try {
            await axios.get('http://127.0.0.1:8000/api/v1/agenticseek-cloud/status');
            setIsOnline(true);
            console.log('Cloud AgenticSeek is online');
        } catch {
            setIsOnline(false);
            console.log('Cloud AgenticSeek is offline');
        }
    };

    const fetchScreenshot = async () => {
        if (!browserMode) return; // Only fetch screenshots in browser mode

        try {
            const timestamp = new Date().getTime();
            const res = await axios.get(`http://127.0.0.1:8000/api/v1/agenticseek/screenshot?timestamp=${timestamp}`, {
                responseType: 'blob'
            });

            if (screenshotUrl) {
                URL.revokeObjectURL(screenshotUrl);
            }

            const imageUrl = URL.createObjectURL(res.data);
            setScreenshotUrl(imageUrl);
            console.log('Screenshot updated');
        } catch (err) {
            console.log('No screenshot available yet');
            setScreenshotUrl(null);

            // Add search activity when browser mode is active
            if (browserMode && isLoading) {
                setSearchActivity(prev => [...prev.slice(-4), {
                    id: Date.now(),
                    type: 'browser',
                    content: 'Navegador en uso - Esperando captura de pantalla...',
                    timestamp: new Date().toLocaleTimeString()
                }]);
            }
        }
    };

    const normalizeAnswer = (answer) => {
        return answer
            .trim()
            .toLowerCase()
            .replace(/\s+/g, ' ')
            .replace(/[.,!?]/g, '')
    };

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    const toggleReasoning = (messageIndex) => {
        setExpandedReasoning(prev => {
            const newSet = new Set(prev);
            if (newSet.has(messageIndex)) {
                newSet.delete(messageIndex);
            } else {
                newSet.add(messageIndex);
            }
            return newSet;
        });
    };

    const fetchLatestAnswer = async () => {
        try {
            const res = await axios.get('http://127.0.0.1:8000/latest_answer');
            const data = res.data;

            updateData(data);
            if (!data.answer || data.answer.trim() === '') {
                return;
            }
            const normalizedNewAnswer = normalizeAnswer(data.answer);
            const answerExists = messages.some(
                (msg) => normalizeAnswer(msg.content) === normalizedNewAnswer
            );
            if (!answerExists) {
                setMessages((prev) => [
                    ...prev,
                    {
                        type: 'agent',
                        content: data.answer,
                        reasoning: data.reasoning,
                        agentName: data.agent_name,
                        status: data.status,
                        uid: data.uid,
                    },
                ]);
                setStatus(data.status);
                scrollToBottom();
            } else {
                console.log('Duplicate answer detected, skipping:', data.answer);
            }
        } catch (error) {
            console.error('Error fetching latest answer:', error);
        }
    };

    const updateData = (data) => {
        setResponseData((prev) => ({
            ...prev,
            blocks: data.blocks || (prev && prev.blocks) || null,
            done: data.done,
            answer: data.answer,
            agent_name: data.agent_name,
            status: data.status,
            uid: data.uid,
        }));
    };

    const handleStop = async (e) => {
        e.preventDefault();
        checkHealth();
        setIsLoading(false);
        setError(null);
        setStatus("Stopped by user");
        // Stop functionality disabled for Emma integration
    }

    const handleFileUpload = (e) => {
        const file = e.target.files?.[0];
        if (file) {
            console.log('File uploaded:', file.name);
            // TODO: Implement file upload functionality
            // For now, just log the file name
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        checkHealth();
        if (!query.trim()) {
            console.log('Empty query');
            return;
        }
        setMessages((prev) => [...prev, { type: 'user', content: query }]);
        setIsLoading(true);
        setError(null);

        try {
            console.log('Sending query:', query);
            setQuery('waiting for response...');

            // Simulate search activity
            setCurrentSearch(`Analizando: "${query}"`);
            setSearchActivity(prev => [...prev, {
                id: Date.now(),
                type: 'query',
                content: `Nueva consulta: ${query}`,
                timestamp: new Date().toLocaleTimeString()
            }]);

            // Add search simulation
            setTimeout(() => {
                setCurrentSearch('🔍 Buscando información relevante...');
                setSearchActivity(prev => [...prev, {
                    id: Date.now() + 1,
                    type: 'search',
                    content: 'Iniciando búsqueda en internet',
                    timestamp: new Date().toLocaleTimeString()
                }]);
            }, 500);

            // Choose endpoint based on mode
            const endpoint = browserMode
                ? 'http://127.0.0.1:8000/api/v1/agenticseek-browser/chat'
                : 'http://127.0.0.1:8000/api/v1/agenticseek-cloud/chat';

            const res = await axios.post(endpoint, {
                message: query
            });
            setQuery('Enter your query...');
            console.log('Response:', res.data);

            // Handle cloud AgenticSeek response format
            if (res.data.success) {
                const data = {
                    answer: res.data.response,
                    agent_name: res.data.agent_used || 'Emma',
                    reasoning: res.data.thinking_process || '',
                    status: 'Completed',
                    uid: Date.now().toString(),
                    sources: res.data.sources || []
                };
                updateData(data);

                // Add agent response to messages
                setMessages((prev) => [
                    ...prev,
                    {
                        type: 'agent',
                        content: res.data.response,
                        agentName: res.data.agent_used || 'Emma',
                        reasoning: res.data.thinking_process || '',
                        sources: res.data.sources || []
                    }
                ]);
            } else {
                // Handle error response
                setError(res.data.error || 'Unknown error occurred');
                setMessages((prev) => [
                    ...prev,
                    { type: 'error', content: `Error: ${res.data.error || 'Unknown error occurred'}` }
                ]);
            }
        } catch (err) {
            console.error('Error:', err);
            setError('Failed to process query.');
            setMessages((prev) => [
                ...prev,
                { type: 'error', content: 'Error: Unable to get a response.' },
            ]);
        } finally {
            console.log('Query completed');
            setIsLoading(false);
            setQuery('');
        }
    };

    // Screenshot functionality disabled for cloud-native version

    return (
        <div className="app">
            <header className="header">
                <div className="header-content">
                    <div className="emma-branding">
                        <img src="/emma-profile.png" alt="Emma AI" className="emma-avatar" />
                        <div className="emma-info">
                            <h1>Emma AI</h1>
                            <p>Tu asistente de IA para marketing</p>
                        </div>
                    </div>
                    <div className="status-badge">
                        <span className={`status-dot ${isOnline ? 'online' : 'offline'}`}></span>
                        {isOnline ? 'En línea' : 'Desconectado'}
                    </div>
                </div>
            </header>
            <main className="main">
                <div className="mode-selector">
                    <button
                        className={`mode-btn ${!browserMode ? 'active' : ''}`}
                        onClick={() => setBrowserMode(false)}
                    >
                        🚀 Cloud Mode (Rápido)
                    </button>
                    <button
                        className={`mode-btn ${browserMode ? 'active' : ''}`}
                        onClick={() => setBrowserMode(true)}
                    >
                        🌐 Browser Mode (Screenshots)
                    </button>
                </div>

                <div className="emma-container">
                    <div className="chat-section">
                        <div className="emma-header">
                            <div className="emma-profile">
                                <div className="emma-avatar-container">
                                    <img
                                        src="/emma-profile.png"
                                        alt="Emma AI"
                                        className="emma-avatar-real"
                                        onError={(e) => {
                                            e.target.style.display = 'none';
                                            e.target.nextSibling.style.display = 'flex';
                                        }}
                                    />
                                    <div className="emma-avatar-placeholder" style={{display: 'none'}}>
                                        🤖
                                    </div>
                                </div>
                                <div className="emma-details">
                                    <h2>Emma AI</h2>
                                    <p>Tu asistente de marketing inteligente</p>
                                    <div className="emma-status">
                                        <span className={`status-dot ${isOnline ? 'online' : 'offline'}`}></span>
                                        {isOnline ? 'En línea y lista para ayudar' : 'Conectando...'}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="messages">
                            {messages.length === 0 ? (
                                <div className="welcome-message">
                                    <div className="welcome-content">
                                        <h3>¡Hola! Soy Emma 👋</h3>
                                        <p>Tu asistente de IA especializada en marketing. Puedo ayudarte con:</p>
                                        <div className="capabilities-grid">
                                            <div className="capability-card">
                                                <span className="capability-emoji">🔍</span>
                                                <span>Investigación de mercado</span>
                                            </div>
                                            <div className="capability-card">
                                                <span className="capability-emoji">📊</span>
                                                <span>Análisis de competencia</span>
                                            </div>
                                            <div className="capability-card">
                                                <span className="capability-emoji">💡</span>
                                                <span>Estrategias de contenido</span>
                                            </div>
                                            <div className="capability-card">
                                                <span className="capability-emoji">🎯</span>
                                                <span>Campañas publicitarias</span>
                                            </div>
                                        </div>
                                        <p className="welcome-cta">¿En qué puedo ayudarte hoy?</p>
                                    </div>
                                </div>
                            ) : (
                                messages.map((msg, index) => (
                                    <div
                                        key={index}
                                        className={`message ${
                                            msg.type === 'user'
                                                ? 'user-message'
                                                : msg.type === 'agent'
                                                ? 'agent-message'
                                                : 'error-message'
                                        }`}
                                    >
                                        <div className="message-header">
                                            {msg.type === 'agent' && (
                                                <div className="agent-header">
                                                    <img
                                                        src="/emma-profile.png"
                                                        alt="Emma AI"
                                                        className="message-avatar-real"
                                                        onError={(e) => {
                                                            e.target.style.display = 'none';
                                                            e.target.nextSibling.style.display = 'flex';
                                                        }}
                                                    />
                                                    <div className="message-avatar-placeholder" style={{display: 'none'}}>
                                                        🤖
                                                    </div>
                                                    <span className="agent-name">{msg.agentName || 'Emma'}</span>
                                                </div>
                                            )}
                                            {msg.type === 'agent' && msg.reasoning && expandedReasoning.has(index) && (
                                                <div className="reasoning-content">
                                                    <ReactMarkdown>{msg.reasoning}</ReactMarkdown>
                                                </div>
                                            )}
                                            {msg.type === 'agent' && (
                                                <button
                                                    className="reasoning-toggle"
                                                    onClick={() => toggleReasoning(index)}
                                                    title={expandedReasoning.has(index) ? "Hide reasoning" : "Show reasoning"}
                                                >
                                                    {expandedReasoning.has(index) ? '▼' : '▶'} Reasoning
                                                </button>
                                            )}
                                        </div>
                                        <div className="message-content">
                                            <ReactMarkdown>{msg.content}</ReactMarkdown>
                                            {msg.sources && msg.sources.length > 0 && (
                                                <div className="sources">
                                                    <h4>Sources:</h4>
                                                    <ul>
                                                        {msg.sources.map((source, idx) => (
                                                            <li key={idx}>
                                                                <a href={source.url} target="_blank" rel="noopener noreferrer">
                                                                    {source.title}
                                                                </a>
                                                            </li>
                                                        ))}
                                                    </ul>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                ))
                            )}
                            <div ref={messagesEndRef} />
                        </div>
                        {isLoading && (
                            <div className="loading-animation">
                                <div className="loading-spinner">
                                    <div className="spinner"></div>
                                    <span>Emma está procesando tu solicitud...</span>
                                </div>
                            </div>
                        )}
                        {!isOnline && (
                            <div className="offline-message">
                                <span>🔴 Emma está desconectada. Verificando conexión...</span>
                            </div>
                        )}

                        <div className="input-section">
                            <form onSubmit={handleSubmit} className="input-form">
                                <div className="input-container">
                                    <div className="input-wrapper">
                                        <input
                                            type="text"
                                            value={query}
                                            onChange={(e) => setQuery(e.target.value)}
                                            placeholder="Escribe tu consulta aquí..."
                                            disabled={isLoading}
                                            className="main-input"
                                        />
                                        <div className="input-actions">
                                            <label className="file-upload-btn" title="Subir archivo">
                                                📎
                                                <input
                                                    type="file"
                                                    onChange={handleFileUpload}
                                                    accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif,.csv,.xlsx,.xls"
                                                    style={{display: 'none'}}
                                                />
                                            </label>
                                            <button type="submit" disabled={isLoading} className="send-btn">
                                                {isLoading ? '⏳' : '📤'} Enviar
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div className="emma-status-section">
                        <div className="status-header">
                            <h2>🤖 Estado de Emma</h2>
                            <div className={`status-indicator ${isOnline ? 'online' : 'offline'}`}>
                                {isOnline ? '🟢 En línea' : '🔴 Desconectada'}
                            </div>
                        </div>

                        <div className="mode-info">
                            <div className="current-mode">
                                <h3>Modo Actual</h3>
                                <div className="mode-display">
                                    {browserMode ? (
                                        <div className="mode-active">
                                            <span className="mode-icon">🌐</span>
                                            <div>
                                                <strong>Browser Mode</strong>
                                                <p>Emma puede navegar y tomar screenshots</p>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="mode-active">
                                            <span className="mode-icon">🚀</span>
                                            <div>
                                                <strong>Cloud Mode</strong>
                                                <p>Respuestas rápidas y eficientes</p>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                        <div className="emma-activity">
                            {error && <div className="error-display">❌ {error}</div>}

                            {isLoading ? (
                                <div className="activity-working">
                                    <div className="working-animation">
                                        <div className="spinner"></div>
                                        <h4>Emma está trabajando...</h4>
                                    </div>
                                    <div className="working-steps">
                                        <div className="step active">🔍 Analizando tu consulta</div>
                                        <div className="step active">🌐 Buscando información</div>
                                        <div className="step">📝 Preparando respuesta</div>
                                    </div>
                                </div>
                            ) : (
                                <div className="activity-idle">
                                    <div className="idle-display">
                                        <div className="idle-icon">💭</div>
                                        <h4>Emma está lista</h4>
                                        <p>Esperando tu próxima consulta</p>
                                    </div>

                                    <div className="emma-capabilities">
                                        <h4>¿Qué puede hacer Emma?</h4>
                                        <div className="capabilities-list">
                                            <div className="capability">
                                                <span>🔍</span>
                                                <span>Investigar mercados y competencia</span>
                                            </div>
                                            <div className="capability">
                                                <span>📊</span>
                                                <span>Analizar datos y tendencias</span>
                                            </div>
                                            <div className="capability">
                                                <span>💡</span>
                                                <span>Crear estrategias de contenido</span>
                                            </div>
                                            <div className="capability">
                                                <span>🎯</span>
                                                <span>Diseñar campañas publicitarias</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {browserMode && screenshotUrl && (
                                <div className="screenshot-preview">
                                    <h4>📸 Vista del navegador</h4>
                                    <img src={screenshotUrl} alt="Emma's Browser" className="screenshot-img" />
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </main>
        </div>
    );
}

export default App;